import 'dart:async';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'shutter_speed_models.dart';

/// A widget for manual shutter speed control with professional photography standards
/// 
/// Features:
/// - Horizontal slider for shutter speed adjustment
/// - Real-time display of current shutter speed value
/// - Auto/manual mode toggle
/// - Debounced updates for performance
/// - Haptic feedback for better UX
/// - Consistent styling with existing CamerAwesome selectors
class AwesomeShutterSpeedSelector extends StatefulWidget {
  final CameraState state;
  final bool showResetButton;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final Color? textColor;
  final EdgeInsets padding;
  final bool showLabel;
  final ShutterSpeedPerformanceConfig? performanceConfig;

  const AwesomeShutterSpeedSelector({
    super.key,
    required this.state,
    this.showResetButton = true,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.textColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
    this.showLabel = true,
    this.performanceConfig,
  });

  @override
  State<AwesomeShutterSpeedSelector> createState() => _AwesomeShutterSpeedSelectorState();
}

class _AwesomeShutterSpeedSelectorState extends State<AwesomeShutterSpeedSelector> {
  int _currentIndex = 0; // Default to auto mode
  Timer? _debounceTimer;
  late ShutterSpeedPerformanceConfig _config;
  bool _isDragging = false;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _config = widget.performanceConfig ?? ShutterSpeedPerformanceConfig.optimal();
    
    // Initialize with current shutter speed from state
    final currentShutterSpeed = widget.state.shutterSpeed;
    _currentIndex = _getIndexFromShutterSpeed(currentShutterSpeed);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }

  int _getIndexFromShutterSpeed(double shutterSpeed) {
    if (shutterSpeed < 0) return 0; // Auto mode
    return ShutterSpeedConstants.getBySpeed(shutterSpeed).index;
  }

  void _onShutterSpeedChanged(double sliderValue) {
    final newIndex = ShutterSpeedConstants.sliderValueToIndex(sliderValue);
    
    setState(() {
      _currentIndex = newIndex;
    });

    // Provide haptic feedback if enabled
    if (_config.enableHapticFeedback) {
      HapticFeedback.selectionClick();
    }

    // Debounce the actual state update to prevent excessive processing
    _debounceTimer?.cancel();
    _debounceTimer = Timer(_config.debounceDuration, () {
      if (mounted) {
        final shutterSpeedValue = ShutterSpeedConstants.getByIndex(_currentIndex);
        final speedInSeconds = shutterSpeedValue.isAuto ? -1.0 : shutterSpeedValue.speedInSeconds;
        widget.state.setShutterSpeed(speedInSeconds);
      }
    });
  }

  void _onShutterSpeedChangeStart(double value) {
    setState(() {
      _isDragging = true;
    });

    // Provide start haptic feedback if enabled
    if (_config.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  void _onShutterSpeedChangeEnd(double value) {
    setState(() {
      _isDragging = false;
    });

    // Provide end haptic feedback if enabled
    if (_config.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }

    // Force immediate update on drag end for better responsiveness
    _debounceTimer?.cancel();
    final shutterSpeedValue = ShutterSpeedConstants.getByIndex(_currentIndex);
    final speedInSeconds = shutterSpeedValue.isAuto ? -1.0 : shutterSpeedValue.speedInSeconds;
    widget.state.setShutterSpeed(speedInSeconds);
  }

  void _resetToAuto() {
    setState(() {
      _currentIndex = 0; // Auto mode
    });

    widget.state.setShutterSpeed(-1.0); // Auto mode

    if (_config.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }
  }

  void _toggleVisibility() {
    setState(() {
      _isVisible = !_isVisible;
    });

    if (_config.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  Widget _buildShutterSpeedPanel(AwesomeTheme theme) {
    final shutterSpeedValue = ShutterSpeedConstants.getByIndex(_currentIndex);
    final sliderValue = ShutterSpeedConstants.indexToSliderValue(_currentIndex);

    return Container(
      padding: widget.padding,
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: theme.bottomActionsBackgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Shutter speed value display
          if (widget.showLabel)
            Container(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Text(
                shutterSpeedValue.displayText,
                style: TextStyle(
                  color: widget.textColor ?? Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

          // Slider with icons
          Row(
            children: [
              // Fast shutter speed icon (left)
              Icon(
                Icons.shutter_speed,
                color: widget.textColor ?? Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),

              // Slider
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: widget.sliderActiveColor ?? Colors.white,
                    inactiveTrackColor: widget.sliderInactiveColor ?? Colors.white30,
                    thumbColor: widget.sliderActiveColor ?? Colors.white,
                    overlayColor: (widget.sliderActiveColor ?? Colors.white).withOpacity(0.2),
                    trackHeight: 2,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6.0),
                  ),
                  child: Slider(
                    value: sliderValue,
                    min: 0.0,
                    max: 1.0,
                    divisions: ShutterSpeedConstants.allValues.length - 1,
                    onChanged: _onShutterSpeedChanged,
                    onChangeStart: _onShutterSpeedChangeStart,
                    onChangeEnd: _onShutterSpeedChangeEnd,
                  ),
                ),
              ),

              const SizedBox(width: 8),
              // Slow shutter speed icon (right)
              Icon(
                Icons.timer,
                color: widget.textColor ?? Colors.white,
                size: 20,
              ),
            ],
          ),

          // Reset button
          if (widget.showResetButton && !shutterSpeedValue.isAuto)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: AwesomeOrientedWidget(
                rotateWithDevice: theme.buttonTheme.rotateWithCamera,
                child: theme.buttonTheme.buttonBuilder(
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    child: Text(
                      "AUTO",
                      style: TextStyle(
                        color: widget.textColor ?? Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  _resetToAuto,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = AwesomeThemeProvider.of(context).theme;
    final buttonTheme = theme.buttonTheme;
    
    return StreamBuilder<SensorConfig>(
      stream: widget.state.sensorConfig$,
      builder: (context, sensorConfigSnapshot) {
        if (!sensorConfigSnapshot.hasData) {
          return const SizedBox.shrink();
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Shutter speed toggle button
            AwesomeOrientedWidget(
              rotateWithDevice: buttonTheme.rotateWithCamera,
              child: buttonTheme.buttonBuilder(
                AwesomeCircleWidget.icon(
                  icon: Icons.shutter_speed,
                  theme: theme,
                ),
                _toggleVisibility,
              ),
            ),
            
            // Shutter speed control panel
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              height: _isVisible ? 120 : 0,
              child: _isVisible ? _buildShutterSpeedPanel(theme) : null,
            ),
          ],
        );
      },
    );
  }
}

/// A compact version of the shutter speed selector without label
class AwesomeCompactShutterSpeedSelector extends StatelessWidget {
  final CameraState state;
  final Color? sliderActiveColor;
  final Color? sliderInactiveColor;
  final EdgeInsets padding;

  const AwesomeCompactShutterSpeedSelector({
    super.key,
    required this.state,
    this.sliderActiveColor,
    this.sliderInactiveColor,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
  });

  @override
  Widget build(BuildContext context) {
    return AwesomeShutterSpeedSelector(
      state: state,
      sliderActiveColor: sliderActiveColor,
      sliderInactiveColor: sliderInactiveColor,
      padding: padding,
      showLabel: false,
    );
  }
}
