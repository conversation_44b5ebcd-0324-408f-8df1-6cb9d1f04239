/// Shutter speed control models and constants for CamerAwesome
/// 
/// This file defines the data structures and constants used for manual
/// shutter speed control, following professional photography standards.

/// Enum representing different shutter speed modes
enum ShutterSpeedMode {
  /// Automatic shutter speed (camera decides)
  auto,
  /// Manual shutter speed (user controlled)
  manual,
}

/// Class representing a shutter speed value with display formatting
class ShutterSpeedValue {
  /// The actual shutter speed in seconds (e.g., 0.001 for 1/1000)
  final double speedInSeconds;
  
  /// Human-readable display text (e.g., "1/125", "2\"", "AUTO")
  final String displayText;
  
  /// Whether this is an auto mode value
  final bool isAuto;
  
  /// Index position in the standard shutter speed array
  final int index;

  const ShutterSpeedValue({
    required this.speedInSeconds,
    required this.displayText,
    required this.isAuto,
    required this.index,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShutterSpeedValue &&
          runtimeType == other.runtimeType &&
          speedInSeconds == other.speedInSeconds &&
          displayText == other.displayText &&
          isAuto == other.isAuto;

  @override
  int get hashCode =>
      speedInSeconds.hashCode ^
      displayText.hashCode ^
      isAuto.hashCode;

  @override
  String toString() => displayText;
}

/// Standard professional photography shutter speeds
class ShutterSpeedConstants {
  /// Auto mode (default)
  static const ShutterSpeedValue auto = ShutterSpeedValue(
    speedInSeconds: -1.0, // Special value for auto mode
    displayText: 'AUTO',
    isAuto: true,
    index: 0,
  );

  /// All available shutter speed values in order from fastest to slowest
  static const List<ShutterSpeedValue> allValues = [
    auto,
    // Fast shutter speeds (fractions)
    ShutterSpeedValue(speedInSeconds: 1/4000, displayText: '1/4000', isAuto: false, index: 1),
    ShutterSpeedValue(speedInSeconds: 1/2000, displayText: '1/2000', isAuto: false, index: 2),
    ShutterSpeedValue(speedInSeconds: 1/1000, displayText: '1/1000', isAuto: false, index: 3),
    ShutterSpeedValue(speedInSeconds: 1/500, displayText: '1/500', isAuto: false, index: 4),
    ShutterSpeedValue(speedInSeconds: 1/250, displayText: '1/250', isAuto: false, index: 5),
    ShutterSpeedValue(speedInSeconds: 1/125, displayText: '1/125', isAuto: false, index: 6),
    ShutterSpeedValue(speedInSeconds: 1/60, displayText: '1/60', isAuto: false, index: 7),
    ShutterSpeedValue(speedInSeconds: 1/30, displayText: '1/30', isAuto: false, index: 8),
    ShutterSpeedValue(speedInSeconds: 1/15, displayText: '1/15', isAuto: false, index: 9),
    ShutterSpeedValue(speedInSeconds: 1/8, displayText: '1/8', isAuto: false, index: 10),
    ShutterSpeedValue(speedInSeconds: 1/4, displayText: '1/4', isAuto: false, index: 11),
    ShutterSpeedValue(speedInSeconds: 1/2, displayText: '1/2', isAuto: false, index: 12),
    // Slow shutter speeds (whole seconds with quotes)
    ShutterSpeedValue(speedInSeconds: 1.0, displayText: '1"', isAuto: false, index: 13),
    ShutterSpeedValue(speedInSeconds: 2.0, displayText: '2"', isAuto: false, index: 14),
    ShutterSpeedValue(speedInSeconds: 4.0, displayText: '4"', isAuto: false, index: 15),
    ShutterSpeedValue(speedInSeconds: 8.0, displayText: '8"', isAuto: false, index: 16),
  ];

  /// Get shutter speed value by index
  static ShutterSpeedValue getByIndex(int index) {
    if (index < 0 || index >= allValues.length) {
      return auto;
    }
    return allValues[index];
  }

  /// Get shutter speed value by speed in seconds
  static ShutterSpeedValue getBySpeed(double speedInSeconds) {
    if (speedInSeconds < 0) return auto;
    
    // Find the closest matching speed
    ShutterSpeedValue closest = auto;
    double minDifference = double.infinity;
    
    for (final value in allValues) {
      if (value.isAuto) continue;
      
      final difference = (value.speedInSeconds - speedInSeconds).abs();
      if (difference < minDifference) {
        minDifference = difference;
        closest = value;
      }
    }
    
    return closest;
  }

  /// Convert slider value (0.0 to 1.0) to shutter speed index
  static int sliderValueToIndex(double sliderValue) {
    final clampedValue = sliderValue.clamp(0.0, 1.0);
    final index = (clampedValue * (allValues.length - 1)).round();
    return index.clamp(0, allValues.length - 1);
  }

  /// Convert shutter speed index to slider value (0.0 to 1.0)
  static double indexToSliderValue(int index) {
    final clampedIndex = index.clamp(0, allValues.length - 1);
    if (allValues.length <= 1) return 0.0;
    return clampedIndex / (allValues.length - 1);
  }

  /// Get the minimum manual shutter speed index (excluding auto)
  static int get minManualIndex => 1;

  /// Get the maximum manual shutter speed index
  static int get maxManualIndex => allValues.length - 1;

  /// Check if an index represents auto mode
  static bool isAutoIndex(int index) => index == 0;

  /// Get manual shutter speeds only (excluding auto)
  static List<ShutterSpeedValue> get manualValues => 
      allValues.where((value) => !value.isAuto).toList();
}

/// Configuration class for shutter speed performance settings
class ShutterSpeedPerformanceConfig {
  /// Duration to debounce shutter speed changes
  final Duration debounceDuration;
  
  /// Whether to enable haptic feedback
  final bool enableHapticFeedback;
  
  /// Whether to show performance warnings
  final bool showPerformanceWarnings;

  const ShutterSpeedPerformanceConfig({
    this.debounceDuration = const Duration(milliseconds: 150),
    this.enableHapticFeedback = true,
    this.showPerformanceWarnings = false,
  });

  /// Default configuration for optimal performance
  factory ShutterSpeedPerformanceConfig.optimal() {
    return const ShutterSpeedPerformanceConfig(
      debounceDuration: Duration(milliseconds: 100),
      enableHapticFeedback: true,
      showPerformanceWarnings: false,
    );
  }

  /// Configuration for high-performance devices
  factory ShutterSpeedPerformanceConfig.highPerformance() {
    return const ShutterSpeedPerformanceConfig(
      debounceDuration: Duration(milliseconds: 50),
      enableHapticFeedback: true,
      showPerformanceWarnings: false,
    );
  }

  /// Configuration for low-performance devices
  factory ShutterSpeedPerformanceConfig.lowPerformance() {
    return const ShutterSpeedPerformanceConfig(
      debounceDuration: Duration(milliseconds: 300),
      enableHapticFeedback: false,
      showPerformanceWarnings: true,
    );
  }
}
