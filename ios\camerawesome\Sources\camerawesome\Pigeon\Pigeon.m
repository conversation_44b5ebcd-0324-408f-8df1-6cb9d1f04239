// Autogenerated from Pig<PERSON> (v9.2.5), do not edit directly.
// See also: https://pub.dev/packages/pigeon

#import "Pigeon.h"
#import <Flutter/Flutter.h>

#if !__has_feature(objc_arc)
#error File requires ARC to be enabled.
#endif

static NSArray *wrapResult(id result, FlutterError *error) {
  if (error) {
    return @[
      error.code ?: [NSNull null], error.message ?: [NSNull null], error.details ?: [NSNull null]
    ];
  }
  return @[ result ?: [NSNull null] ];
}
static id GetNullableObjectAtIndex(NSArray *array, NSInteger key) {
  id result = array[key];
  return (result == [NSNull null]) ? nil : result;
}

@interface PreviewSize ()
+ (PreviewSize *)fromList:(NSArray *)list;
+ (nullable PreviewSize *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface ExifPreferences ()
+ (ExifPreferences *)fromList:(NSArray *)list;
+ (nullable ExifPreferences *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonSensor ()
+ (PigeonSensor *)fromList:(NSArray *)list;
+ (nullable PigeonSensor *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface VideoOptions ()
+ (VideoOptions *)fromList:(NSArray *)list;
+ (nullable VideoOptions *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface AndroidVideoOptions ()
+ (AndroidVideoOptions *)fromList:(NSArray *)list;
+ (nullable AndroidVideoOptions *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface CupertinoVideoOptions ()
+ (CupertinoVideoOptions *)fromList:(NSArray *)list;
+ (nullable CupertinoVideoOptions *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PigeonSensorTypeDevice ()
+ (PigeonSensorTypeDevice *)fromList:(NSArray *)list;
+ (nullable PigeonSensorTypeDevice *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface AndroidFocusSettings ()
+ (AndroidFocusSettings *)fromList:(NSArray *)list;
+ (nullable AndroidFocusSettings *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface PlaneWrapper ()
+ (PlaneWrapper *)fromList:(NSArray *)list;
+ (nullable PlaneWrapper *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface CropRectWrapper ()
+ (CropRectWrapper *)fromList:(NSArray *)list;
+ (nullable CropRectWrapper *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@interface AnalysisImageWrapper ()
+ (AnalysisImageWrapper *)fromList:(NSArray *)list;
+ (nullable AnalysisImageWrapper *)nullableFromList:(NSArray *)list;
- (NSArray *)toList;
@end

@implementation PreviewSize
+ (instancetype)makeWithWidth:(NSNumber *)width
    height:(NSNumber *)height {
  PreviewSize* pigeonResult = [[PreviewSize alloc] init];
  pigeonResult.width = width;
  pigeonResult.height = height;
  return pigeonResult;
}
+ (PreviewSize *)fromList:(NSArray *)list {
  PreviewSize *pigeonResult = [[PreviewSize alloc] init];
  pigeonResult.width = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.width != nil, @"");
  pigeonResult.height = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.height != nil, @"");
  return pigeonResult;
}
+ (nullable PreviewSize *)nullableFromList:(NSArray *)list {
  return (list) ? [PreviewSize fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.width ?: [NSNull null]),
    (self.height ?: [NSNull null]),
  ];
}
@end

@implementation ExifPreferences
+ (instancetype)makeWithSaveGPSLocation:(NSNumber *)saveGPSLocation {
  ExifPreferences* pigeonResult = [[ExifPreferences alloc] init];
  pigeonResult.saveGPSLocation = saveGPSLocation;
  return pigeonResult;
}
+ (ExifPreferences *)fromList:(NSArray *)list {
  ExifPreferences *pigeonResult = [[ExifPreferences alloc] init];
  pigeonResult.saveGPSLocation = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.saveGPSLocation != nil, @"");
  return pigeonResult;
}
+ (nullable ExifPreferences *)nullableFromList:(NSArray *)list {
  return (list) ? [ExifPreferences fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.saveGPSLocation ?: [NSNull null]),
  ];
}
@end

@implementation PigeonSensor
+ (instancetype)makeWithPosition:(PigeonSensorPosition)position
    type:(PigeonSensorType)type
    deviceId:(nullable NSString *)deviceId {
  PigeonSensor* pigeonResult = [[PigeonSensor alloc] init];
  pigeonResult.position = position;
  pigeonResult.type = type;
  pigeonResult.deviceId = deviceId;
  return pigeonResult;
}
+ (PigeonSensor *)fromList:(NSArray *)list {
  PigeonSensor *pigeonResult = [[PigeonSensor alloc] init];
  pigeonResult.position = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.type = [GetNullableObjectAtIndex(list, 1) integerValue];
  pigeonResult.deviceId = GetNullableObjectAtIndex(list, 2);
  return pigeonResult;
}
+ (nullable PigeonSensor *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonSensor fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.position),
    @(self.type),
    (self.deviceId ?: [NSNull null]),
  ];
}
@end

@implementation VideoOptions
+ (instancetype)makeWithEnableAudio:(NSNumber *)enableAudio
    quality:(VideoRecordingQuality)quality
    android:(nullable AndroidVideoOptions *)android
    ios:(nullable CupertinoVideoOptions *)ios {
  VideoOptions* pigeonResult = [[VideoOptions alloc] init];
  pigeonResult.enableAudio = enableAudio;
  pigeonResult.quality = quality;
  pigeonResult.android = android;
  pigeonResult.ios = ios;
  return pigeonResult;
}
+ (VideoOptions *)fromList:(NSArray *)list {
  VideoOptions *pigeonResult = [[VideoOptions alloc] init];
  pigeonResult.enableAudio = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.enableAudio != nil, @"");
  pigeonResult.quality = [GetNullableObjectAtIndex(list, 1) integerValue];
  pigeonResult.android = [AndroidVideoOptions nullableFromList:(GetNullableObjectAtIndex(list, 2))];
  pigeonResult.ios = [CupertinoVideoOptions nullableFromList:(GetNullableObjectAtIndex(list, 3))];
  return pigeonResult;
}
+ (nullable VideoOptions *)nullableFromList:(NSArray *)list {
  return (list) ? [VideoOptions fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.enableAudio ?: [NSNull null]),
    @(self.quality),
    (self.android ? [self.android toList] : [NSNull null]),
    (self.ios ? [self.ios toList] : [NSNull null]),
  ];
}
@end

@implementation AndroidVideoOptions
+ (instancetype)makeWithBitrate:(nullable NSNumber *)bitrate
    fallbackStrategy:(QualityFallbackStrategy)fallbackStrategy {
  AndroidVideoOptions* pigeonResult = [[AndroidVideoOptions alloc] init];
  pigeonResult.bitrate = bitrate;
  pigeonResult.fallbackStrategy = fallbackStrategy;
  return pigeonResult;
}
+ (AndroidVideoOptions *)fromList:(NSArray *)list {
  AndroidVideoOptions *pigeonResult = [[AndroidVideoOptions alloc] init];
  pigeonResult.bitrate = GetNullableObjectAtIndex(list, 0);
  pigeonResult.fallbackStrategy = [GetNullableObjectAtIndex(list, 1) integerValue];
  return pigeonResult;
}
+ (nullable AndroidVideoOptions *)nullableFromList:(NSArray *)list {
  return (list) ? [AndroidVideoOptions fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.bitrate ?: [NSNull null]),
    @(self.fallbackStrategy),
  ];
}
@end

@implementation CupertinoVideoOptions
+ (instancetype)makeWithFileType:(CupertinoFileType)fileType
    codec:(CupertinoCodecType)codec
    fps:(nullable NSNumber *)fps {
  CupertinoVideoOptions* pigeonResult = [[CupertinoVideoOptions alloc] init];
  pigeonResult.fileType = fileType;
  pigeonResult.codec = codec;
  pigeonResult.fps = fps;
  return pigeonResult;
}
+ (CupertinoVideoOptions *)fromList:(NSArray *)list {
  CupertinoVideoOptions *pigeonResult = [[CupertinoVideoOptions alloc] init];
  pigeonResult.fileType = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.codec = [GetNullableObjectAtIndex(list, 1) integerValue];
  pigeonResult.fps = GetNullableObjectAtIndex(list, 2);
  return pigeonResult;
}
+ (nullable CupertinoVideoOptions *)nullableFromList:(NSArray *)list {
  return (list) ? [CupertinoVideoOptions fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.fileType),
    @(self.codec),
    (self.fps ?: [NSNull null]),
  ];
}
@end

@implementation PigeonSensorTypeDevice
+ (instancetype)makeWithSensorType:(PigeonSensorType)sensorType
    name:(NSString *)name
    iso:(NSNumber *)iso
    flashAvailable:(NSNumber *)flashAvailable
    uid:(NSString *)uid {
  PigeonSensorTypeDevice* pigeonResult = [[PigeonSensorTypeDevice alloc] init];
  pigeonResult.sensorType = sensorType;
  pigeonResult.name = name;
  pigeonResult.iso = iso;
  pigeonResult.flashAvailable = flashAvailable;
  pigeonResult.uid = uid;
  return pigeonResult;
}
+ (PigeonSensorTypeDevice *)fromList:(NSArray *)list {
  PigeonSensorTypeDevice *pigeonResult = [[PigeonSensorTypeDevice alloc] init];
  pigeonResult.sensorType = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.name = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.name != nil, @"");
  pigeonResult.iso = GetNullableObjectAtIndex(list, 2);
  NSAssert(pigeonResult.iso != nil, @"");
  pigeonResult.flashAvailable = GetNullableObjectAtIndex(list, 3);
  NSAssert(pigeonResult.flashAvailable != nil, @"");
  pigeonResult.uid = GetNullableObjectAtIndex(list, 4);
  NSAssert(pigeonResult.uid != nil, @"");
  return pigeonResult;
}
+ (nullable PigeonSensorTypeDevice *)nullableFromList:(NSArray *)list {
  return (list) ? [PigeonSensorTypeDevice fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.sensorType),
    (self.name ?: [NSNull null]),
    (self.iso ?: [NSNull null]),
    (self.flashAvailable ?: [NSNull null]),
    (self.uid ?: [NSNull null]),
  ];
}
@end

@implementation AndroidFocusSettings
+ (instancetype)makeWithAutoCancelDurationInMillis:(NSNumber *)autoCancelDurationInMillis {
  AndroidFocusSettings* pigeonResult = [[AndroidFocusSettings alloc] init];
  pigeonResult.autoCancelDurationInMillis = autoCancelDurationInMillis;
  return pigeonResult;
}
+ (AndroidFocusSettings *)fromList:(NSArray *)list {
  AndroidFocusSettings *pigeonResult = [[AndroidFocusSettings alloc] init];
  pigeonResult.autoCancelDurationInMillis = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.autoCancelDurationInMillis != nil, @"");
  return pigeonResult;
}
+ (nullable AndroidFocusSettings *)nullableFromList:(NSArray *)list {
  return (list) ? [AndroidFocusSettings fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.autoCancelDurationInMillis ?: [NSNull null]),
  ];
}
@end

@implementation PlaneWrapper
+ (instancetype)makeWithBytes:(FlutterStandardTypedData *)bytes
    bytesPerRow:(NSNumber *)bytesPerRow
    bytesPerPixel:(nullable NSNumber *)bytesPerPixel
    width:(nullable NSNumber *)width
    height:(nullable NSNumber *)height {
  PlaneWrapper* pigeonResult = [[PlaneWrapper alloc] init];
  pigeonResult.bytes = bytes;
  pigeonResult.bytesPerRow = bytesPerRow;
  pigeonResult.bytesPerPixel = bytesPerPixel;
  pigeonResult.width = width;
  pigeonResult.height = height;
  return pigeonResult;
}
+ (PlaneWrapper *)fromList:(NSArray *)list {
  PlaneWrapper *pigeonResult = [[PlaneWrapper alloc] init];
  pigeonResult.bytes = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.bytes != nil, @"");
  pigeonResult.bytesPerRow = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.bytesPerRow != nil, @"");
  pigeonResult.bytesPerPixel = GetNullableObjectAtIndex(list, 2);
  pigeonResult.width = GetNullableObjectAtIndex(list, 3);
  pigeonResult.height = GetNullableObjectAtIndex(list, 4);
  return pigeonResult;
}
+ (nullable PlaneWrapper *)nullableFromList:(NSArray *)list {
  return (list) ? [PlaneWrapper fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.bytes ?: [NSNull null]),
    (self.bytesPerRow ?: [NSNull null]),
    (self.bytesPerPixel ?: [NSNull null]),
    (self.width ?: [NSNull null]),
    (self.height ?: [NSNull null]),
  ];
}
@end

@implementation CropRectWrapper
+ (instancetype)makeWithLeft:(NSNumber *)left
    top:(NSNumber *)top
    width:(NSNumber *)width
    height:(NSNumber *)height {
  CropRectWrapper* pigeonResult = [[CropRectWrapper alloc] init];
  pigeonResult.left = left;
  pigeonResult.top = top;
  pigeonResult.width = width;
  pigeonResult.height = height;
  return pigeonResult;
}
+ (CropRectWrapper *)fromList:(NSArray *)list {
  CropRectWrapper *pigeonResult = [[CropRectWrapper alloc] init];
  pigeonResult.left = GetNullableObjectAtIndex(list, 0);
  NSAssert(pigeonResult.left != nil, @"");
  pigeonResult.top = GetNullableObjectAtIndex(list, 1);
  NSAssert(pigeonResult.top != nil, @"");
  pigeonResult.width = GetNullableObjectAtIndex(list, 2);
  NSAssert(pigeonResult.width != nil, @"");
  pigeonResult.height = GetNullableObjectAtIndex(list, 3);
  NSAssert(pigeonResult.height != nil, @"");
  return pigeonResult;
}
+ (nullable CropRectWrapper *)nullableFromList:(NSArray *)list {
  return (list) ? [CropRectWrapper fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    (self.left ?: [NSNull null]),
    (self.top ?: [NSNull null]),
    (self.width ?: [NSNull null]),
    (self.height ?: [NSNull null]),
  ];
}
@end

@implementation AnalysisImageWrapper
+ (instancetype)makeWithFormat:(AnalysisImageFormat)format
    bytes:(nullable FlutterStandardTypedData *)bytes
    width:(NSNumber *)width
    height:(NSNumber *)height
    planes:(nullable NSArray<PlaneWrapper *> *)planes
    cropRect:(nullable CropRectWrapper *)cropRect
    rotation:(AnalysisRotation)rotation {
  AnalysisImageWrapper* pigeonResult = [[AnalysisImageWrapper alloc] init];
  pigeonResult.format = format;
  pigeonResult.bytes = bytes;
  pigeonResult.width = width;
  pigeonResult.height = height;
  pigeonResult.planes = planes;
  pigeonResult.cropRect = cropRect;
  pigeonResult.rotation = rotation;
  return pigeonResult;
}
+ (AnalysisImageWrapper *)fromList:(NSArray *)list {
  AnalysisImageWrapper *pigeonResult = [[AnalysisImageWrapper alloc] init];
  pigeonResult.format = [GetNullableObjectAtIndex(list, 0) integerValue];
  pigeonResult.bytes = GetNullableObjectAtIndex(list, 1);
  pigeonResult.width = GetNullableObjectAtIndex(list, 2);
  NSAssert(pigeonResult.width != nil, @"");
  pigeonResult.height = GetNullableObjectAtIndex(list, 3);
  NSAssert(pigeonResult.height != nil, @"");
  pigeonResult.planes = GetNullableObjectAtIndex(list, 4);
  pigeonResult.cropRect = [CropRectWrapper nullableFromList:(GetNullableObjectAtIndex(list, 5))];
  pigeonResult.rotation = [GetNullableObjectAtIndex(list, 6) integerValue];
  return pigeonResult;
}
+ (nullable AnalysisImageWrapper *)nullableFromList:(NSArray *)list {
  return (list) ? [AnalysisImageWrapper fromList:list] : nil;
}
- (NSArray *)toList {
  return @[
    @(self.format),
    (self.bytes ?: [NSNull null]),
    (self.width ?: [NSNull null]),
    (self.height ?: [NSNull null]),
    (self.planes ?: [NSNull null]),
    (self.cropRect ? [self.cropRect toList] : [NSNull null]),
    @(self.rotation),
  ];
}
@end

@interface AnalysisImageUtilsCodecReader : FlutterStandardReader
@end
@implementation AnalysisImageUtilsCodecReader
- (nullable id)readValueOfType:(UInt8)type {
  switch (type) {
    case 128: 
      return [AnalysisImageWrapper fromList:[self readValue]];
    case 129: 
      return [CropRectWrapper fromList:[self readValue]];
    case 130: 
      return [PlaneWrapper fromList:[self readValue]];
    default:
      return [super readValueOfType:type];
  }
}
@end

@interface AnalysisImageUtilsCodecWriter : FlutterStandardWriter
@end
@implementation AnalysisImageUtilsCodecWriter
- (void)writeValue:(id)value {
  if ([value isKindOfClass:[AnalysisImageWrapper class]]) {
    [self writeByte:128];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[CropRectWrapper class]]) {
    [self writeByte:129];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PlaneWrapper class]]) {
    [self writeByte:130];
    [self writeValue:[value toList]];
  } else {
    [super writeValue:value];
  }
}
@end

@interface AnalysisImageUtilsCodecReaderWriter : FlutterStandardReaderWriter
@end
@implementation AnalysisImageUtilsCodecReaderWriter
- (FlutterStandardWriter *)writerWithData:(NSMutableData *)data {
  return [[AnalysisImageUtilsCodecWriter alloc] initWithData:data];
}
- (FlutterStandardReader *)readerWithData:(NSData *)data {
  return [[AnalysisImageUtilsCodecReader alloc] initWithData:data];
}
@end

NSObject<FlutterMessageCodec> *AnalysisImageUtilsGetCodec(void) {
  static FlutterStandardMessageCodec *sSharedObject = nil;
  static dispatch_once_t sPred = 0;
  dispatch_once(&sPred, ^{
    AnalysisImageUtilsCodecReaderWriter *readerWriter = [[AnalysisImageUtilsCodecReaderWriter alloc] init];
    sSharedObject = [FlutterStandardMessageCodec codecWithReaderWriter:readerWriter];
  });
  return sSharedObject;
}

void AnalysisImageUtilsSetup(id<FlutterBinaryMessenger> binaryMessenger, NSObject<AnalysisImageUtils> *api) {
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.AnalysisImageUtils.nv21toJpeg"
        binaryMessenger:binaryMessenger
        codec:AnalysisImageUtilsGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(nv21toJpegNv21Image:jpegQuality:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(nv21toJpegNv21Image:jpegQuality:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        AnalysisImageWrapper *arg_nv21Image = GetNullableObjectAtIndex(args, 0);
        NSNumber *arg_jpegQuality = GetNullableObjectAtIndex(args, 1);
        [api nv21toJpegNv21Image:arg_nv21Image jpegQuality:arg_jpegQuality completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.AnalysisImageUtils.yuv420toJpeg"
        binaryMessenger:binaryMessenger
        codec:AnalysisImageUtilsGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(yuv420toJpegYuvImage:jpegQuality:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(yuv420toJpegYuvImage:jpegQuality:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        AnalysisImageWrapper *arg_yuvImage = GetNullableObjectAtIndex(args, 0);
        NSNumber *arg_jpegQuality = GetNullableObjectAtIndex(args, 1);
        [api yuv420toJpegYuvImage:arg_yuvImage jpegQuality:arg_jpegQuality completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.AnalysisImageUtils.yuv420toNv21"
        binaryMessenger:binaryMessenger
        codec:AnalysisImageUtilsGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(yuv420toNv21YuvImage:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(yuv420toNv21YuvImage:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        AnalysisImageWrapper *arg_yuvImage = GetNullableObjectAtIndex(args, 0);
        [api yuv420toNv21YuvImage:arg_yuvImage completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.AnalysisImageUtils.bgra8888toJpeg"
        binaryMessenger:binaryMessenger
        codec:AnalysisImageUtilsGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(bgra8888toJpegBgra8888image:jpegQuality:completion:)], @"AnalysisImageUtils api (%@) doesn't respond to @selector(bgra8888toJpegBgra8888image:jpegQuality:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        AnalysisImageWrapper *arg_bgra8888image = GetNullableObjectAtIndex(args, 0);
        NSNumber *arg_jpegQuality = GetNullableObjectAtIndex(args, 1);
        [api bgra8888toJpegBgra8888image:arg_bgra8888image jpegQuality:arg_jpegQuality completion:^(AnalysisImageWrapper *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}
@interface CameraInterfaceCodecReader : FlutterStandardReader
@end
@implementation CameraInterfaceCodecReader
- (nullable id)readValueOfType:(UInt8)type {
  switch (type) {
    case 128: 
      return [AndroidFocusSettings fromList:[self readValue]];
    case 129: 
      return [AndroidVideoOptions fromList:[self readValue]];
    case 130: 
      return [CupertinoVideoOptions fromList:[self readValue]];
    case 131: 
      return [ExifPreferences fromList:[self readValue]];
    case 132: 
      return [PigeonSensor fromList:[self readValue]];
    case 133: 
      return [PigeonSensorTypeDevice fromList:[self readValue]];
    case 134: 
      return [PreviewSize fromList:[self readValue]];
    case 135: 
      return [PreviewSize fromList:[self readValue]];
    case 136: 
      return [VideoOptions fromList:[self readValue]];
    default:
      return [super readValueOfType:type];
  }
}
@end

@interface CameraInterfaceCodecWriter : FlutterStandardWriter
@end
@implementation CameraInterfaceCodecWriter
- (void)writeValue:(id)value {
  if ([value isKindOfClass:[AndroidFocusSettings class]]) {
    [self writeByte:128];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[AndroidVideoOptions class]]) {
    [self writeByte:129];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[CupertinoVideoOptions class]]) {
    [self writeByte:130];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[ExifPreferences class]]) {
    [self writeByte:131];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonSensor class]]) {
    [self writeByte:132];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PigeonSensorTypeDevice class]]) {
    [self writeByte:133];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PreviewSize class]]) {
    [self writeByte:134];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[PreviewSize class]]) {
    [self writeByte:135];
    [self writeValue:[value toList]];
  } else if ([value isKindOfClass:[VideoOptions class]]) {
    [self writeByte:136];
    [self writeValue:[value toList]];
  } else {
    [super writeValue:value];
  }
}
@end

@interface CameraInterfaceCodecReaderWriter : FlutterStandardReaderWriter
@end
@implementation CameraInterfaceCodecReaderWriter
- (FlutterStandardWriter *)writerWithData:(NSMutableData *)data {
  return [[CameraInterfaceCodecWriter alloc] initWithData:data];
}
- (FlutterStandardReader *)readerWithData:(NSData *)data {
  return [[CameraInterfaceCodecReader alloc] initWithData:data];
}
@end

NSObject<FlutterMessageCodec> *CameraInterfaceGetCodec(void) {
  static FlutterStandardMessageCodec *sSharedObject = nil;
  static dispatch_once_t sPred = 0;
  dispatch_once(&sPred, ^{
    CameraInterfaceCodecReaderWriter *readerWriter = [[CameraInterfaceCodecReaderWriter alloc] init];
    sSharedObject = [FlutterStandardMessageCodec codecWithReaderWriter:readerWriter];
  });
  return sSharedObject;
}

void CameraInterfaceSetup(id<FlutterBinaryMessenger> binaryMessenger, NSObject<CameraInterface> *api) {
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setupCamera"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setupCameraSensors:aspectRatio:zoom:mirrorFrontCamera:enablePhysicalButton:flashMode:captureMode:enableImageStream:exifPreferences:videoOptions:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(setupCameraSensors:aspectRatio:zoom:mirrorFrontCamera:enablePhysicalButton:flashMode:captureMode:enableImageStream:exifPreferences:videoOptions:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        NSString *arg_aspectRatio = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_zoom = GetNullableObjectAtIndex(args, 2);
        NSNumber *arg_mirrorFrontCamera = GetNullableObjectAtIndex(args, 3);
        NSNumber *arg_enablePhysicalButton = GetNullableObjectAtIndex(args, 4);
        NSString *arg_flashMode = GetNullableObjectAtIndex(args, 5);
        NSString *arg_captureMode = GetNullableObjectAtIndex(args, 6);
        NSNumber *arg_enableImageStream = GetNullableObjectAtIndex(args, 7);
        ExifPreferences *arg_exifPreferences = GetNullableObjectAtIndex(args, 8);
        VideoOptions *arg_videoOptions = GetNullableObjectAtIndex(args, 9);
        [api setupCameraSensors:arg_sensors aspectRatio:arg_aspectRatio zoom:arg_zoom mirrorFrontCamera:arg_mirrorFrontCamera enablePhysicalButton:arg_enablePhysicalButton flashMode:arg_flashMode captureMode:arg_captureMode enableImageStream:arg_enableImageStream exifPreferences:arg_exifPreferences videoOptions:arg_videoOptions completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.checkPermissions"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(checkPermissionsPermissions:error:)], @"CameraInterface api (%@) doesn't respond to @selector(checkPermissionsPermissions:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSArray<NSString *> *arg_permissions = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        NSArray<NSString *> *output = [api checkPermissionsPermissions:arg_permissions error:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// Returns given [CamerAwesomePermission] list (as String). Location permission might be
  /// refused but the app should still be able to run.
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.requestPermissions"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(requestPermissionsSaveGpsLocation:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(requestPermissionsSaveGpsLocation:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_saveGpsLocation = GetNullableObjectAtIndex(args, 0);
        [api requestPermissionsSaveGpsLocation:arg_saveGpsLocation completion:^(NSArray<NSString *> *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.getPreviewTextureId"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getPreviewTextureIdCameraPosition:error:)], @"CameraInterface api (%@) doesn't respond to @selector(getPreviewTextureIdCameraPosition:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_cameraPosition = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        NSNumber *output = [api getPreviewTextureIdCameraPosition:arg_cameraPosition error:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.takePhoto"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(takePhotoSensors:paths:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(takePhotoSensors:paths:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        NSArray<NSString *> *arg_paths = GetNullableObjectAtIndex(args, 1);
        [api takePhotoSensors:arg_sensors paths:arg_paths completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.recordVideo"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(recordVideoSensors:paths:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(recordVideoSensors:paths:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        NSArray<NSString *> *arg_paths = GetNullableObjectAtIndex(args, 1);
        [api recordVideoSensors:arg_sensors paths:arg_paths completion:^(FlutterError *_Nullable error) {
          callback(wrapResult(nil, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.pauseVideoRecording"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(pauseVideoRecordingWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(pauseVideoRecordingWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api pauseVideoRecordingWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.resumeVideoRecording"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(resumeVideoRecordingWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(resumeVideoRecordingWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api resumeVideoRecordingWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.receivedImageFromStream"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(receivedImageFromStreamWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(receivedImageFromStreamWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api receivedImageFromStreamWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.stopRecordingVideo"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopRecordingVideoWithCompletion:)], @"CameraInterface api (%@) doesn't respond to @selector(stopRecordingVideoWithCompletion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        [api stopRecordingVideoWithCompletion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.getFrontSensors"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getFrontSensorsWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getFrontSensorsWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSArray<PigeonSensorTypeDevice *> *output = [api getFrontSensorsWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.getBackSensors"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getBackSensorsWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getBackSensorsWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSArray<PigeonSensorTypeDevice *> *output = [api getBackSensorsWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.start"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(startWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(startWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api startWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.stop"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(stopWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api stopWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setFlashMode"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setFlashModeMode:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setFlashModeMode:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_mode = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setFlashModeMode:arg_mode error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.handleAutoFocus"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(handleAutoFocusWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(handleAutoFocusWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api handleAutoFocusWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  /// Starts auto focus on a point at ([x], [y]).
  ///
  /// On Android, you can control after how much time you want to switch back
  /// to passive focus mode with [androidFocusSettings].
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.focusOnPoint"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(focusOnPointPreviewSize:x:y:androidFocusSettings:error:)], @"CameraInterface api (%@) doesn't respond to @selector(focusOnPointPreviewSize:x:y:androidFocusSettings:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        PreviewSize *arg_previewSize = GetNullableObjectAtIndex(args, 0);
        NSNumber *arg_x = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_y = GetNullableObjectAtIndex(args, 2);
        AndroidFocusSettings *arg_androidFocusSettings = GetNullableObjectAtIndex(args, 3);
        FlutterError *error;
        [api focusOnPointPreviewSize:arg_previewSize x:arg_x y:arg_y androidFocusSettings:arg_androidFocusSettings error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setZoom"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setZoomZoom:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setZoomZoom:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_zoom = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setZoomZoom:arg_zoom error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setMirrorFrontCamera"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setMirrorFrontCameraMirror:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setMirrorFrontCameraMirror:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_mirror = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setMirrorFrontCameraMirror:arg_mirror error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setSensor"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setSensorSensors:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setSensorSensors:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSArray<PigeonSensor *> *arg_sensors = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setSensorSensors:arg_sensors error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setCorrection"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setCorrectionBrightness:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setCorrectionBrightness:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_brightness = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setCorrectionBrightness:arg_brightness error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.getMinZoom"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getMinZoomWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getMinZoomWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api getMinZoomWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.getMaxZoom"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getMaxZoomWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(getMaxZoomWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api getMaxZoomWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setCaptureMode"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setCaptureModeMode:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setCaptureModeMode:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_mode = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setCaptureModeMode:arg_mode error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setRecordingAudioMode"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setRecordingAudioModeEnableAudio:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(setRecordingAudioModeEnableAudio:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_enableAudio = GetNullableObjectAtIndex(args, 0);
        [api setRecordingAudioModeEnableAudio:arg_enableAudio completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.availableSizes"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(availableSizesWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(availableSizesWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSArray<PreviewSize *> *output = [api availableSizesWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.refresh"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(refreshWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(refreshWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api refreshWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.getEffectivPreviewSize"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(getEffectivPreviewSizeIndex:error:)], @"CameraInterface api (%@) doesn't respond to @selector(getEffectivPreviewSizeIndex:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSNumber *arg_index = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        PreviewSize *output = [api getEffectivPreviewSizeIndex:arg_index error:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setPhotoSize"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setPhotoSizeSize:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setPhotoSizeSize:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        PreviewSize *arg_size = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setPhotoSizeSize:arg_size error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setPreviewSize"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setPreviewSizeSize:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setPreviewSizeSize:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        PreviewSize *arg_size = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setPreviewSizeSize:arg_size error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setAspectRatio"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setAspectRatioAspectRatio:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setAspectRatioAspectRatio:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_aspectRatio = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setAspectRatioAspectRatio:arg_aspectRatio error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setupImageAnalysisStream"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setupImageAnalysisStreamFormat:width:maxFramesPerSecond:autoStart:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setupImageAnalysisStreamFormat:width:maxFramesPerSecond:autoStart:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSString *arg_format = GetNullableObjectAtIndex(args, 0);
        NSNumber *arg_width = GetNullableObjectAtIndex(args, 1);
        NSNumber *arg_maxFramesPerSecond = GetNullableObjectAtIndex(args, 2);
        NSNumber *arg_autoStart = GetNullableObjectAtIndex(args, 3);
        FlutterError *error;
        [api setupImageAnalysisStreamFormat:arg_format width:arg_width maxFramesPerSecond:arg_maxFramesPerSecond autoStart:arg_autoStart error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setExifPreferences"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setExifPreferencesExifPreferences:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(setExifPreferencesExifPreferences:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        ExifPreferences *arg_exifPreferences = GetNullableObjectAtIndex(args, 0);
        [api setExifPreferencesExifPreferences:arg_exifPreferences completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.startAnalysis"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(startAnalysisWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(startAnalysisWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api startAnalysisWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.stopAnalysis"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(stopAnalysisWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(stopAnalysisWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        [api stopAnalysisWithError:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.setFilter"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(setFilterMatrix:error:)], @"CameraInterface api (%@) doesn't respond to @selector(setFilterMatrix:error:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        NSArray<NSNumber *> *arg_matrix = GetNullableObjectAtIndex(args, 0);
        FlutterError *error;
        [api setFilterMatrix:arg_matrix error:&error];
        callback(wrapResult(nil, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.isVideoRecordingAndImageAnalysisSupported"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(isVideoRecordingAndImageAnalysisSupportedSensor:completion:)], @"CameraInterface api (%@) doesn't respond to @selector(isVideoRecordingAndImageAnalysisSupportedSensor:completion:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        NSArray *args = message;
        PigeonSensorPosition arg_sensor = [GetNullableObjectAtIndex(args, 0) integerValue];
        [api isVideoRecordingAndImageAnalysisSupportedSensor:arg_sensor completion:^(NSNumber *_Nullable output, FlutterError *_Nullable error) {
          callback(wrapResult(output, error));
        }];
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
  {
    FlutterBasicMessageChannel *channel =
      [[FlutterBasicMessageChannel alloc]
        initWithName:@"dev.flutter.pigeon.CameraInterface.isMultiCamSupported"
        binaryMessenger:binaryMessenger
        codec:CameraInterfaceGetCodec()];
    if (api) {
      NSCAssert([api respondsToSelector:@selector(isMultiCamSupportedWithError:)], @"CameraInterface api (%@) doesn't respond to @selector(isMultiCamSupportedWithError:)", api);
      [channel setMessageHandler:^(id _Nullable message, FlutterReply callback) {
        FlutterError *error;
        NSNumber *output = [api isMultiCamSupportedWithError:&error];
        callback(wrapResult(output, error));
      }];
    } else {
      [channel setMessageHandler:nil];
    }
  }
}
