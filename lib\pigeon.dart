// Autogenerated from <PERSON><PERSON> (v9.2.5), do not edit directly.
// See also: https://pub.dev/packages/pigeon
// ignore_for_file: public_member_api_docs, non_constant_identifier_names, avoid_as, unused_import, unnecessary_parenthesis, prefer_null_aware_operators, omit_local_variable_types, unused_shown_name, unnecessary_import

import 'dart:async';
import 'dart:typed_data' show Float<PERSON>List, Int32List, Int64List, Uint8List;

import 'package:flutter/foundation.dart' show ReadBuffer, WriteBuffer;
import 'package:flutter/services.dart';

enum PigeonSensorPosition {
  back,
  front,
  unknown,
}

/// Video recording quality, from [sd] to [uhd], with [highest] and [lowest] to
/// let the device choose the best/worst quality available.
/// [highest] is the default quality.
///
/// Qualities are defined like this:
/// [sd] < [hd] < [fhd] < [uhd]
enum VideoRecordingQuality {
  lowest,
  sd,
  hd,
  fhd,
  uhd,
  highest,
}

/// If the specified [VideoRecordingQuality] is not available on the device,
/// the [VideoRecordingQuality] will fallback to [higher] or [lower] quality.
/// [higher] is the default fallback strategy.
enum QualityFallbackStrategy {
  higher,
  lower,
}

enum CupertinoFileType {
  quickTimeMovie,
  mpeg4,
  appleM4V,
  type3GPP,
  type3GPP2,
}

enum CupertinoCodecType {
  h264,
  hevc,
  hevcWithAlpha,
  jpeg,
  appleProRes4444,
  appleProRes422,
  appleProRes422HQ,
  appleProRes422LT,
  appleProRes422Proxy,
}

enum PigeonSensorType {
  /// A built-in wide-angle camera.
  ///
  /// The wide angle sensor is the default sensor for iOS
  wideAngle,

  /// A built-in camera with a shorter focal length than that of the wide-angle camera.
  ultraWideAngle,

  /// A built-in camera device with a longer focal length than the wide-angle camera.
  telephoto,

  /// A device that consists of two cameras, one Infrared and one YUV.
  ///
  /// iOS only
  trueDepth,
  unknown,
}

enum CamerAwesomePermission {
  storage,
  camera,
  location,
  recordAudio,
}

enum AnalysisImageFormat {
  yuv_420,
  bgra8888,
  jpeg,
  nv21,
  unknown,
}

enum AnalysisRotation {
  rotation0deg,
  rotation90deg,
  rotation180deg,
  rotation270deg,
}

class PreviewSize {
  PreviewSize({
    required this.width,
    required this.height,
  });

  double width;

  double height;

  Object encode() {
    return <Object?>[
      width,
      height,
    ];
  }

  static PreviewSize decode(Object result) {
    result as List<Object?>;
    return PreviewSize(
      width: result[0]! as double,
      height: result[1]! as double,
    );
  }

  Size toSize() => Size(width, height);

  /// Returns a new [PreviewSize] with [width] and [height] inverted.
  /// Useful when the preview size is given in portrait mode but the camera
  /// is in landscape mode.
  /// Ex : for tablets, the preview size is given in landscape mode but the device is in portrait mode.
  inverted() => PreviewSize(width: height, height: width);
}

class ExifPreferences {
  ExifPreferences({
    required this.saveGPSLocation,
  });

  bool saveGPSLocation;

  Object encode() {
    return <Object?>[
      saveGPSLocation,
    ];
  }

  static ExifPreferences decode(Object result) {
    result as List<Object?>;
    return ExifPreferences(
      saveGPSLocation: result[0]! as bool,
    );
  }
}

class PigeonSensor {
  PigeonSensor({
    required this.position,
    required this.type,
    this.deviceId,
  });

  PigeonSensorPosition position;

  PigeonSensorType type;

  String? deviceId;

  Object encode() {
    return <Object?>[
      position.index,
      type.index,
      deviceId,
    ];
  }

  static PigeonSensor decode(Object result) {
    result as List<Object?>;
    return PigeonSensor(
      position: PigeonSensorPosition.values[result[0]! as int],
      type: PigeonSensorType.values[result[1]! as int],
      deviceId: result[2] as String?,
    );
  }
}

/// Video recording options. Some of them are specific to each platform.
class VideoOptions {
  VideoOptions({
    required this.enableAudio,
    this.quality,
    this.android,
    this.ios,
  });

  /// Enable audio while video recording
  bool enableAudio;

  /// The quality of the video recording, defaults to [VideoRecordingQuality.highest].
  VideoRecordingQuality? quality;

  AndroidVideoOptions? android;

  CupertinoVideoOptions? ios;

  Object encode() {
    return <Object?>[
      enableAudio,
      quality?.index,
      android?.encode(),
      ios?.encode(),
    ];
  }

  static VideoOptions decode(Object result) {
    result as List<Object?>;
    return VideoOptions(
      enableAudio: result[0]! as bool,
      quality: result[1] != null
          ? VideoRecordingQuality.values[result[1]! as int]
          : null,
      android: result[2] != null
          ? AndroidVideoOptions.decode(result[2]! as List<Object?>)
          : null,
      ios: result[3] != null
          ? CupertinoVideoOptions.decode(result[3]! as List<Object?>)
          : null,
    );
  }
}

class AndroidVideoOptions {
  AndroidVideoOptions({
    this.bitrate,
    this.fallbackStrategy,
  });

  /// The bitrate of the video recording. Only set it if a custom bitrate is
  /// desired.
  int? bitrate;

  QualityFallbackStrategy? fallbackStrategy;

  Object encode() {
    return <Object?>[
      bitrate,
      fallbackStrategy?.index,
    ];
  }

  static AndroidVideoOptions decode(Object result) {
    result as List<Object?>;
    return AndroidVideoOptions(
      bitrate: result[0] as int?,
      fallbackStrategy: result[1] != null
          ? QualityFallbackStrategy.values[result[1]! as int]
          : null,
    );
  }
}

class CupertinoVideoOptions {
  CupertinoVideoOptions({
    this.fileType,
    this.codec,
    this.fps,
  });

  /// Specify video file type, defaults to [AVFileTypeQuickTimeMovie].
  CupertinoFileType? fileType;

  /// Specify video codec, defaults to [AVVideoCodecTypeH264].
  CupertinoCodecType? codec;

  /// Specify video fps, defaults to [30].
  int? fps;

  Object encode() {
    return <Object?>[
      fileType?.index,
      codec?.index,
      fps,
    ];
  }

  static CupertinoVideoOptions decode(Object result) {
    result as List<Object?>;
    return CupertinoVideoOptions(
      fileType: result[0] != null
          ? CupertinoFileType.values[result[0]! as int]
          : null,
      codec: result[1] != null
          ? CupertinoCodecType.values[result[1]! as int]
          : null,
      fps: result[2] as int?,
    );
  }
}

class PigeonSensorTypeDevice {
  PigeonSensorTypeDevice({
    required this.sensorType,
    required this.name,
    required this.iso,
    required this.flashAvailable,
    required this.uid,
  });

  PigeonSensorType sensorType;

  /// A localized device name for display in the user interface.
  String name;

  /// The current exposure ISO value.
  double iso;

  /// A Boolean value that indicates whether the flash is currently available for use.
  bool flashAvailable;

  /// An identifier that uniquely identifies the device.
  String uid;

  Object encode() {
    return <Object?>[
      sensorType.index,
      name,
      iso,
      flashAvailable,
      uid,
    ];
  }

  static PigeonSensorTypeDevice decode(Object result) {
    result as List<Object?>;
    return PigeonSensorTypeDevice(
      sensorType: PigeonSensorType.values[result[0]! as int],
      name: result[1]! as String,
      iso: result[2]! as double,
      flashAvailable: result[3]! as bool,
      uid: result[4]! as String,
    );
  }
}

class AndroidFocusSettings {
  AndroidFocusSettings({
    required this.autoCancelDurationInMillis,
  });

  /// The auto focus will be canceled after the given [autoCancelDurationInMillis].
  /// If [autoCancelDurationInMillis] is equals to 0 (or less), the auto focus
  /// will **not** be canceled. A manual `focusOnPoint` call will be needed to
  /// focus on an other point.
  /// Minimal duration of [autoCancelDurationInMillis] is 1000 ms. If set
  /// between 0 (exclusive) and 1000 (exclusive), it will be raised to 1000.
  int autoCancelDurationInMillis;

  Object encode() {
    return <Object?>[
      autoCancelDurationInMillis,
    ];
  }

  static AndroidFocusSettings decode(Object result) {
    result as List<Object?>;
    return AndroidFocusSettings(
      autoCancelDurationInMillis: result[0]! as int,
    );
  }
}

class PlaneWrapper {
  PlaneWrapper({
    required this.bytes,
    required this.bytesPerRow,
    this.bytesPerPixel,
    this.width,
    this.height,
  });

  Uint8List bytes;

  int bytesPerRow;

  int? bytesPerPixel;

  int? width;

  int? height;

  Object encode() {
    return <Object?>[
      bytes,
      bytesPerRow,
      bytesPerPixel,
      width,
      height,
    ];
  }

  static PlaneWrapper decode(Object result) {
    result as List<Object?>;
    return PlaneWrapper(
      bytes: result[0]! as Uint8List,
      bytesPerRow: result[1]! as int,
      bytesPerPixel: result[2] as int?,
      width: result[3] as int?,
      height: result[4] as int?,
    );
  }
}

class CropRectWrapper {
  CropRectWrapper({
    required this.left,
    required this.top,
    required this.width,
    required this.height,
  });

  int left;

  int top;

  int width;

  int height;

  Object encode() {
    return <Object?>[
      left,
      top,
      width,
      height,
    ];
  }

  static CropRectWrapper decode(Object result) {
    result as List<Object?>;
    return CropRectWrapper(
      left: result[0]! as int,
      top: result[1]! as int,
      width: result[2]! as int,
      height: result[3]! as int,
    );
  }
}

class AnalysisImageWrapper {
  AnalysisImageWrapper({
    required this.format,
    this.bytes,
    required this.width,
    required this.height,
    this.planes,
    this.cropRect,
    this.rotation,
  });

  AnalysisImageFormat format;

  Uint8List? bytes;

  int width;

  int height;

  List<PlaneWrapper?>? planes;

  CropRectWrapper? cropRect;

  AnalysisRotation? rotation;

  Object encode() {
    return <Object?>[
      format.index,
      bytes,
      width,
      height,
      planes,
      cropRect?.encode(),
      rotation?.index,
    ];
  }

  static AnalysisImageWrapper decode(Object result) {
    result as List<Object?>;
    return AnalysisImageWrapper(
      format: AnalysisImageFormat.values[result[0]! as int],
      bytes: result[1] as Uint8List?,
      width: result[2]! as int,
      height: result[3]! as int,
      planes: (result[4] as List<Object?>?)?.cast<PlaneWrapper?>(),
      cropRect: result[5] != null
          ? CropRectWrapper.decode(result[5]! as List<Object?>)
          : null,
      rotation:
          result[6] != null ? AnalysisRotation.values[result[6]! as int] : null,
    );
  }
}

class _AnalysisImageUtilsCodec extends StandardMessageCodec {
  const _AnalysisImageUtilsCodec();

  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is AnalysisImageWrapper) {
      buffer.putUint8(128);
      writeValue(buffer, value.encode());
    } else if (value is CropRectWrapper) {
      buffer.putUint8(129);
      writeValue(buffer, value.encode());
    } else if (value is PlaneWrapper) {
      buffer.putUint8(130);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 128:
        return AnalysisImageWrapper.decode(readValue(buffer)!);
      case 129:
        return CropRectWrapper.decode(readValue(buffer)!);
      case 130:
        return PlaneWrapper.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

class AnalysisImageUtils {
  /// Constructor for [AnalysisImageUtils].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  AnalysisImageUtils({BinaryMessenger? binaryMessenger})
      : _binaryMessenger = binaryMessenger;
  final BinaryMessenger? _binaryMessenger;

  static const MessageCodec<Object?> codec = _AnalysisImageUtilsCodec();

  Future<AnalysisImageWrapper> nv21toJpeg(
      AnalysisImageWrapper arg_nv21Image, int arg_jpegQuality) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.AnalysisImageUtils.nv21toJpeg', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel
        .send(<Object?>[arg_nv21Image, arg_jpegQuality]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as AnalysisImageWrapper?)!;
    }
  }

  Future<AnalysisImageWrapper> yuv420toJpeg(
      AnalysisImageWrapper arg_yuvImage, int arg_jpegQuality) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.AnalysisImageUtils.yuv420toJpeg', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel
        .send(<Object?>[arg_yuvImage, arg_jpegQuality]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as AnalysisImageWrapper?)!;
    }
  }

  Future<AnalysisImageWrapper> yuv420toNv21(
      AnalysisImageWrapper arg_yuvImage) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.AnalysisImageUtils.yuv420toNv21', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_yuvImage]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as AnalysisImageWrapper?)!;
    }
  }

  Future<AnalysisImageWrapper> bgra8888toJpeg(
      AnalysisImageWrapper arg_bgra8888image, int arg_jpegQuality) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.AnalysisImageUtils.bgra8888toJpeg', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel
        .send(<Object?>[arg_bgra8888image, arg_jpegQuality]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as AnalysisImageWrapper?)!;
    }
  }
}

class _CameraInterfaceCodec extends StandardMessageCodec {
  const _CameraInterfaceCodec();

  @override
  void writeValue(WriteBuffer buffer, Object? value) {
    if (value is AndroidFocusSettings) {
      buffer.putUint8(128);
      writeValue(buffer, value.encode());
    } else if (value is AndroidVideoOptions) {
      buffer.putUint8(129);
      writeValue(buffer, value.encode());
    } else if (value is CupertinoVideoOptions) {
      buffer.putUint8(130);
      writeValue(buffer, value.encode());
    } else if (value is ExifPreferences) {
      buffer.putUint8(131);
      writeValue(buffer, value.encode());
    } else if (value is PigeonSensor) {
      buffer.putUint8(132);
      writeValue(buffer, value.encode());
    } else if (value is PigeonSensorTypeDevice) {
      buffer.putUint8(133);
      writeValue(buffer, value.encode());
    } else if (value is PreviewSize) {
      buffer.putUint8(134);
      writeValue(buffer, value.encode());
    } else if (value is PreviewSize) {
      buffer.putUint8(135);
      writeValue(buffer, value.encode());
    } else if (value is VideoOptions) {
      buffer.putUint8(136);
      writeValue(buffer, value.encode());
    } else {
      super.writeValue(buffer, value);
    }
  }

  @override
  Object? readValueOfType(int type, ReadBuffer buffer) {
    switch (type) {
      case 128:
        return AndroidFocusSettings.decode(readValue(buffer)!);
      case 129:
        return AndroidVideoOptions.decode(readValue(buffer)!);
      case 130:
        return CupertinoVideoOptions.decode(readValue(buffer)!);
      case 131:
        return ExifPreferences.decode(readValue(buffer)!);
      case 132:
        return PigeonSensor.decode(readValue(buffer)!);
      case 133:
        return PigeonSensorTypeDevice.decode(readValue(buffer)!);
      case 134:
        return PreviewSize.decode(readValue(buffer)!);
      case 135:
        return PreviewSize.decode(readValue(buffer)!);
      case 136:
        return VideoOptions.decode(readValue(buffer)!);
      default:
        return super.readValueOfType(type, buffer);
    }
  }
}

class CameraInterface {
  /// Constructor for [CameraInterface].  The [binaryMessenger] named argument is
  /// available for dependency injection.  If it is left null, the default
  /// BinaryMessenger will be used which routes to the host platform.
  CameraInterface({BinaryMessenger? binaryMessenger})
      : _binaryMessenger = binaryMessenger;
  final BinaryMessenger? _binaryMessenger;

  static const MessageCodec<Object?> codec = _CameraInterfaceCodec();

  Future<bool> setupCamera(
      List<PigeonSensor?> arg_sensors,
      String arg_aspectRatio,
      double arg_zoom,
      bool arg_mirrorFrontCamera,
      bool arg_enablePhysicalButton,
      String arg_flashMode,
      String arg_captureMode,
      bool arg_enableImageStream,
      ExifPreferences arg_exifPreferences,
      VideoOptions? arg_videoOptions) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setupCamera', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(<Object?>[
      arg_sensors,
      arg_aspectRatio,
      arg_zoom,
      arg_mirrorFrontCamera,
      arg_enablePhysicalButton,
      arg_flashMode,
      arg_captureMode,
      arg_enableImageStream,
      arg_exifPreferences,
      arg_videoOptions
    ]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<List<String?>> checkPermissions(List<String?> arg_permissions) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.checkPermissions', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_permissions]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as List<Object?>?)!.cast<String?>();
    }
  }

  /// Returns given [CamerAwesomePermission] list (as String). Location permission might be
  /// refused but the app should still be able to run.
  Future<List<String?>> requestPermissions(bool arg_saveGpsLocation) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.requestPermissions', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_saveGpsLocation]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as List<Object?>?)!.cast<String?>();
    }
  }

  Future<int> getPreviewTextureId(int arg_cameraPosition) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.getPreviewTextureId', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_cameraPosition]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as int?)!;
    }
  }

  Future<bool> takePhoto(
      List<PigeonSensor?> arg_sensors, List<String?> arg_paths) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.takePhoto', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_sensors, arg_paths]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<void> recordVideo(
      List<PigeonSensor?> arg_sensors, List<String?> arg_paths) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.recordVideo', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_sensors, arg_paths]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> pauseVideoRecording() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.pauseVideoRecording', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> resumeVideoRecording() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.resumeVideoRecording', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> receivedImageFromStream() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.receivedImageFromStream', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> stopRecordingVideo() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.stopRecordingVideo', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<List<PigeonSensorTypeDevice?>> getFrontSensors() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.getFrontSensors', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as List<Object?>?)!.cast<PigeonSensorTypeDevice?>();
    }
  }

  Future<List<PigeonSensorTypeDevice?>> getBackSensors() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.getBackSensors', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as List<Object?>?)!.cast<PigeonSensorTypeDevice?>();
    }
  }

  Future<bool> start() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.start', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<bool> stop() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.stop', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<void> setFlashMode(String arg_mode) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setFlashMode', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_mode]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> handleAutoFocus() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.handleAutoFocus', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  /// Starts auto focus on a point at ([x], [y]).
  ///
  /// On Android, you can control after how much time you want to switch back
  /// to passive focus mode with [androidFocusSettings].
  Future<void> focusOnPoint(PreviewSize arg_previewSize, double arg_x,
      double arg_y, AndroidFocusSettings? arg_androidFocusSettings) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.focusOnPoint', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(
            <Object?>[arg_previewSize, arg_x, arg_y, arg_androidFocusSettings])
        as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setZoom(double arg_zoom) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setZoom', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_zoom]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setMirrorFrontCamera(bool arg_mirror) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setMirrorFrontCamera', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_mirror]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setSensor(List<PigeonSensor?> arg_sensors) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setSensor', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_sensors]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setCorrection(double arg_brightness) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setCorrection', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_brightness]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<double> getMinZoom() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.getMinZoom', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as double?)!;
    }
  }

  Future<double> getMaxZoom() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.getMaxZoom', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as double?)!;
    }
  }

  Future<void> setCaptureMode(String arg_mode) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setCaptureMode', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_mode]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> setRecordingAudioMode(bool arg_enableAudio) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setRecordingAudioMode', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_enableAudio]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<List<PreviewSize?>> availableSizes() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.availableSizes', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as List<Object?>?)!.cast<PreviewSize?>();
    }
  }

  Future<void> refresh() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.refresh', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<PreviewSize?> getEffectivPreviewSize(int arg_index) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.getEffectivPreviewSize', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_index]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return (replyList[0] as PreviewSize?);
    }
  }

  Future<void> setPhotoSize(PreviewSize arg_size) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setPhotoSize', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_size]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setPreviewSize(PreviewSize arg_size) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setPreviewSize', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_size]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setAspectRatio(String arg_aspectRatio) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setAspectRatio', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_aspectRatio]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setupImageAnalysisStream(String arg_format, int arg_width,
      double? arg_maxFramesPerSecond, bool arg_autoStart) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setupImageAnalysisStream', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(<Object?>[
      arg_format,
      arg_width,
      arg_maxFramesPerSecond,
      arg_autoStart
    ]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> setExifPreferences(ExifPreferences arg_exifPreferences) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setExifPreferences', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_exifPreferences]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<void> startAnalysis() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.startAnalysis', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> stopAnalysis() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.stopAnalysis', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<void> setFilter(List<double?> arg_matrix) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.setFilter', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_matrix]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else {
      return;
    }
  }

  Future<bool> isVideoRecordingAndImageAnalysisSupported(
      PigeonSensorPosition arg_sensor) async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.isVideoRecordingAndImageAnalysisSupported',
        codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList =
        await channel.send(<Object?>[arg_sensor.index]) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }

  Future<bool> isMultiCamSupported() async {
    final BasicMessageChannel<Object?> channel = BasicMessageChannel<Object?>(
        'dev.flutter.pigeon.CameraInterface.isMultiCamSupported', codec,
        binaryMessenger: _binaryMessenger);
    final List<Object?>? replyList = await channel.send(null) as List<Object?>?;
    if (replyList == null) {
      throw PlatformException(
        code: 'channel-error',
        message: 'Unable to establish connection on channel.',
      );
    } else if (replyList.length > 1) {
      throw PlatformException(
        code: replyList[0]! as String,
        message: replyList[1] as String?,
        details: replyList[2],
      );
    } else if (replyList[0] == null) {
      throw PlatformException(
        code: 'null-error',
        message: 'Host platform returned null value for non-null return value.',
      );
    } else {
      return (replyList[0] as bool?)!;
    }
  }
}
