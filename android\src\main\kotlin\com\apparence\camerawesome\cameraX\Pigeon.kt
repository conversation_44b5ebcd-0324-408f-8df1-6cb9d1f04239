// Autogenerated from <PERSON><PERSON> (v9.2.5), do not edit directly.
// See also: https://pub.dev/packages/pigeon

package com.apparence.camerawesome.cameraX

import android.util.Log
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.StandardMessageCodec
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

private fun wrapResult(result: Any?): List<Any?> {
  return listOf(result)
}

private fun wrapError(exception: Throwable): List<Any?> {
  if (exception is FlutterError) {
    return listOf(
      exception.code,
      exception.message,
      exception.details
    )
  } else {
    return listOf(
      exception.javaClass.simpleName,
      exception.toString(),
      "Cause: " + exception.cause + ", Stacktrace: " + Log.getStackTraceString(exception)
    )
  }
}

/**
 * Error class for passing custom error details to Flutter via a thrown PlatformException.
 * @property code The error code.
 * @property message The error message.
 * @property details The error details. Must be a datatype supported by the api codec.
 */
class FlutterError (
  val code: String,
  override val message: String? = null,
  val details: Any? = null
) : Throwable()

enum class PigeonSensorPosition(val raw: Int) {
  BACK(0),
  FRONT(1),
  UNKNOWN(2);

  companion object {
    fun ofRaw(raw: Int): PigeonSensorPosition? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * Video recording quality, from [sd] to [uhd], with [highest] and [lowest] to
 * let the device choose the best/worst quality available.
 * [highest] is the default quality.
 *
 * Qualities are defined like this:
 * [sd] < [hd] < [fhd] < [uhd]
 */
enum class VideoRecordingQuality(val raw: Int) {
  LOWEST(0),
  SD(1),
  HD(2),
  FHD(3),
  UHD(4),
  HIGHEST(5);

  companion object {
    fun ofRaw(raw: Int): VideoRecordingQuality? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/**
 * If the specified [VideoRecordingQuality] is not available on the device,
 * the [VideoRecordingQuality] will fallback to [higher] or [lower] quality.
 * [higher] is the default fallback strategy.
 */
enum class QualityFallbackStrategy(val raw: Int) {
  HIGHER(0),
  LOWER(1);

  companion object {
    fun ofRaw(raw: Int): QualityFallbackStrategy? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class CupertinoFileType(val raw: Int) {
  QUICKTIMEMOVIE(0),
  MPEG4(1),
  APPLEM4V(2),
  TYPE3GPP(3),
  TYPE3GPP2(4);

  companion object {
    fun ofRaw(raw: Int): CupertinoFileType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class CupertinoCodecType(val raw: Int) {
  H264(0),
  HEVC(1),
  HEVCWITHALPHA(2),
  JPEG(3),
  APPLEPRORES4444(4),
  APPLEPRORES422(5),
  APPLEPRORES422HQ(6),
  APPLEPRORES422LT(7),
  APPLEPRORES422PROXY(8);

  companion object {
    fun ofRaw(raw: Int): CupertinoCodecType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class PigeonSensorType(val raw: Int) {
  /**
   * A built-in wide-angle camera.
   *
   * The wide angle sensor is the default sensor for iOS
   */
  WIDEANGLE(0),
  /** A built-in camera with a shorter focal length than that of the wide-angle camera. */
  ULTRAWIDEANGLE(1),
  /** A built-in camera device with a longer focal length than the wide-angle camera. */
  TELEPHOTO(2),
  /**
   * A device that consists of two cameras, one Infrared and one YUV.
   *
   * iOS only
   */
  TRUEDEPTH(3),
  UNKNOWN(4);

  companion object {
    fun ofRaw(raw: Int): PigeonSensorType? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class CamerAwesomePermission(val raw: Int) {
  STORAGE(0),
  CAMERA(1),
  LOCATION(2),
  RECORD_AUDIO(3);

  companion object {
    fun ofRaw(raw: Int): CamerAwesomePermission? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class AnalysisImageFormat(val raw: Int) {
  YUV_420(0),
  BGRA8888(1),
  JPEG(2),
  NV21(3),
  UNKNOWN(4);

  companion object {
    fun ofRaw(raw: Int): AnalysisImageFormat? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

enum class AnalysisRotation(val raw: Int) {
  ROTATION0DEG(0),
  ROTATION90DEG(1),
  ROTATION180DEG(2),
  ROTATION270DEG(3);

  companion object {
    fun ofRaw(raw: Int): AnalysisRotation? {
      return values().firstOrNull { it.raw == raw }
    }
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PreviewSize (
  val width: Double,
  val height: Double

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): PreviewSize {
      val width = list[0] as Double
      val height = list[1] as Double
      return PreviewSize(width, height)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      width,
      height,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class ExifPreferences (
  val saveGPSLocation: Boolean

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): ExifPreferences {
      val saveGPSLocation = list[0] as Boolean
      return ExifPreferences(saveGPSLocation)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      saveGPSLocation,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PigeonSensor (
  val position: PigeonSensorPosition,
  val type: PigeonSensorType,
  val deviceId: String? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): PigeonSensor {
      val position = PigeonSensorPosition.ofRaw(list[0] as Int)!!
      val type = PigeonSensorType.ofRaw(list[1] as Int)!!
      val deviceId = list[2] as String?
      return PigeonSensor(position, type, deviceId)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      position.raw,
      type.raw,
      deviceId,
    )
  }
}

/**
 * Video recording options. Some of them are specific to each platform.
 *
 * Generated class from Pigeon that represents data sent in messages.
 */
data class VideoOptions (
  /** Enable audio while video recording */
  val enableAudio: Boolean,
  /** The quality of the video recording, defaults to [VideoRecordingQuality.highest]. */
  val quality: VideoRecordingQuality? = null,
  val android: AndroidVideoOptions? = null,
  val ios: CupertinoVideoOptions? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): VideoOptions {
      val enableAudio = list[0] as Boolean
      val quality: VideoRecordingQuality? = (list[1] as Int?)?.let {
        VideoRecordingQuality.ofRaw(it)
      }
      val android: AndroidVideoOptions? = (list[2] as List<Any?>?)?.let {
        AndroidVideoOptions.fromList(it)
      }
      val ios: CupertinoVideoOptions? = (list[3] as List<Any?>?)?.let {
        CupertinoVideoOptions.fromList(it)
      }
      return VideoOptions(enableAudio, quality, android, ios)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      enableAudio,
      quality?.raw,
      android?.toList(),
      ios?.toList(),
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class AndroidVideoOptions (
  /**
   * The bitrate of the video recording. Only set it if a custom bitrate is
   * desired.
   */
  val bitrate: Long? = null,
  val fallbackStrategy: QualityFallbackStrategy? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): AndroidVideoOptions {
      val bitrate = list[0].let { if (it is Int) it.toLong() else it as Long? }
      val fallbackStrategy: QualityFallbackStrategy? = (list[1] as Int?)?.let {
        QualityFallbackStrategy.ofRaw(it)
      }
      return AndroidVideoOptions(bitrate, fallbackStrategy)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      bitrate,
      fallbackStrategy?.raw,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class CupertinoVideoOptions (
  /** Specify video file type, defaults to [AVFileTypeQuickTimeMovie]. */
  val fileType: CupertinoFileType? = null,
  /** Specify video codec, defaults to [AVVideoCodecTypeH264]. */
  val codec: CupertinoCodecType? = null,
  /** Specify video fps, defaults to [30]. */
  val fps: Long? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): CupertinoVideoOptions {
      val fileType: CupertinoFileType? = (list[0] as Int?)?.let {
        CupertinoFileType.ofRaw(it)
      }
      val codec: CupertinoCodecType? = (list[1] as Int?)?.let {
        CupertinoCodecType.ofRaw(it)
      }
      val fps = list[2].let { if (it is Int) it.toLong() else it as Long? }
      return CupertinoVideoOptions(fileType, codec, fps)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      fileType?.raw,
      codec?.raw,
      fps,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PigeonSensorTypeDevice (
  val sensorType: PigeonSensorType,
  /** A localized device name for display in the user interface. */
  val name: String,
  /** The current exposure ISO value. */
  val iso: Double,
  /** A Boolean value that indicates whether the flash is currently available for use. */
  val flashAvailable: Boolean,
  /** An identifier that uniquely identifies the device. */
  val uid: String

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): PigeonSensorTypeDevice {
      val sensorType = PigeonSensorType.ofRaw(list[0] as Int)!!
      val name = list[1] as String
      val iso = list[2] as Double
      val flashAvailable = list[3] as Boolean
      val uid = list[4] as String
      return PigeonSensorTypeDevice(sensorType, name, iso, flashAvailable, uid)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      sensorType.raw,
      name,
      iso,
      flashAvailable,
      uid,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class AndroidFocusSettings (
  /**
   * The auto focus will be canceled after the given [autoCancelDurationInMillis].
   * If [autoCancelDurationInMillis] is equals to 0 (or less), the auto focus
   * will **not** be canceled. A manual `focusOnPoint` call will be needed to
   * focus on an other point.
   * Minimal duration of [autoCancelDurationInMillis] is 1000 ms. If set
   * between 0 (exclusive) and 1000 (exclusive), it will be raised to 1000.
   */
  val autoCancelDurationInMillis: Long

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): AndroidFocusSettings {
      val autoCancelDurationInMillis = list[0].let { if (it is Int) it.toLong() else it as Long }
      return AndroidFocusSettings(autoCancelDurationInMillis)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      autoCancelDurationInMillis,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class PlaneWrapper (
  val bytes: ByteArray,
  val bytesPerRow: Long,
  val bytesPerPixel: Long? = null,
  val width: Long? = null,
  val height: Long? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): PlaneWrapper {
      val bytes = list[0] as ByteArray
      val bytesPerRow = list[1].let { if (it is Int) it.toLong() else it as Long }
      val bytesPerPixel = list[2].let { if (it is Int) it.toLong() else it as Long? }
      val width = list[3].let { if (it is Int) it.toLong() else it as Long? }
      val height = list[4].let { if (it is Int) it.toLong() else it as Long? }
      return PlaneWrapper(bytes, bytesPerRow, bytesPerPixel, width, height)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      bytes,
      bytesPerRow,
      bytesPerPixel,
      width,
      height,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class CropRectWrapper (
  val left: Long,
  val top: Long,
  val width: Long,
  val height: Long

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): CropRectWrapper {
      val left = list[0].let { if (it is Int) it.toLong() else it as Long }
      val top = list[1].let { if (it is Int) it.toLong() else it as Long }
      val width = list[2].let { if (it is Int) it.toLong() else it as Long }
      val height = list[3].let { if (it is Int) it.toLong() else it as Long }
      return CropRectWrapper(left, top, width, height)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      left,
      top,
      width,
      height,
    )
  }
}

/** Generated class from Pigeon that represents data sent in messages. */
data class AnalysisImageWrapper (
  val format: AnalysisImageFormat,
  val bytes: ByteArray? = null,
  val width: Long,
  val height: Long,
  val planes: List<PlaneWrapper?>? = null,
  val cropRect: CropRectWrapper? = null,
  val rotation: AnalysisRotation? = null

) {
  companion object {
    @Suppress("UNCHECKED_CAST")
    fun fromList(list: List<Any?>): AnalysisImageWrapper {
      val format = AnalysisImageFormat.ofRaw(list[0] as Int)!!
      val bytes = list[1] as ByteArray?
      val width = list[2].let { if (it is Int) it.toLong() else it as Long }
      val height = list[3].let { if (it is Int) it.toLong() else it as Long }
      val planes = list[4] as List<PlaneWrapper?>?
      val cropRect: CropRectWrapper? = (list[5] as List<Any?>?)?.let {
        CropRectWrapper.fromList(it)
      }
      val rotation: AnalysisRotation? = (list[6] as Int?)?.let {
        AnalysisRotation.ofRaw(it)
      }
      return AnalysisImageWrapper(format, bytes, width, height, planes, cropRect, rotation)
    }
  }
  fun toList(): List<Any?> {
    return listOf<Any?>(
      format.raw,
      bytes,
      width,
      height,
      planes,
      cropRect?.toList(),
      rotation?.raw,
    )
  }
}

@Suppress("UNCHECKED_CAST")
private object AnalysisImageUtilsCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      128.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          AnalysisImageWrapper.fromList(it)
        }
      }
      129.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CropRectWrapper.fromList(it)
        }
      }
      130.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PlaneWrapper.fromList(it)
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is AnalysisImageWrapper -> {
        stream.write(128)
        writeValue(stream, value.toList())
      }
      is CropRectWrapper -> {
        stream.write(129)
        writeValue(stream, value.toList())
      }
      is PlaneWrapper -> {
        stream.write(130)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}

/** Generated interface from Pigeon that represents a handler of messages from Flutter. */
interface AnalysisImageUtils {
  fun nv21toJpeg(nv21Image: AnalysisImageWrapper, jpegQuality: Long, callback: (Result<AnalysisImageWrapper>) -> Unit)
  fun yuv420toJpeg(yuvImage: AnalysisImageWrapper, jpegQuality: Long, callback: (Result<AnalysisImageWrapper>) -> Unit)
  fun yuv420toNv21(yuvImage: AnalysisImageWrapper, callback: (Result<AnalysisImageWrapper>) -> Unit)
  fun bgra8888toJpeg(bgra8888image: AnalysisImageWrapper, jpegQuality: Long, callback: (Result<AnalysisImageWrapper>) -> Unit)

  companion object {
    /** The codec used by AnalysisImageUtils. */
    val codec: MessageCodec<Any?> by lazy {
      AnalysisImageUtilsCodec
    }
    /** Sets up an instance of `AnalysisImageUtils` to handle messages through the `binaryMessenger`. */
    @Suppress("UNCHECKED_CAST")
    fun setUp(binaryMessenger: BinaryMessenger, api: AnalysisImageUtils?) {
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.AnalysisImageUtils.nv21toJpeg", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val nv21ImageArg = args[0] as AnalysisImageWrapper
            val jpegQualityArg = args[1].let { if (it is Int) it.toLong() else it as Long }
            api.nv21toJpeg(nv21ImageArg, jpegQualityArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.AnalysisImageUtils.yuv420toJpeg", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val yuvImageArg = args[0] as AnalysisImageWrapper
            val jpegQualityArg = args[1].let { if (it is Int) it.toLong() else it as Long }
            api.yuv420toJpeg(yuvImageArg, jpegQualityArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.AnalysisImageUtils.yuv420toNv21", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val yuvImageArg = args[0] as AnalysisImageWrapper
            api.yuv420toNv21(yuvImageArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.AnalysisImageUtils.bgra8888toJpeg", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val bgra8888imageArg = args[0] as AnalysisImageWrapper
            val jpegQualityArg = args[1].let { if (it is Int) it.toLong() else it as Long }
            api.bgra8888toJpeg(bgra8888imageArg, jpegQualityArg) { result: Result<AnalysisImageWrapper> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
@Suppress("UNCHECKED_CAST")
private object CameraInterfaceCodec : StandardMessageCodec() {
  override fun readValueOfType(type: Byte, buffer: ByteBuffer): Any? {
    return when (type) {
      128.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          AndroidFocusSettings.fromList(it)
        }
      }
      129.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          AndroidVideoOptions.fromList(it)
        }
      }
      130.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          CupertinoVideoOptions.fromList(it)
        }
      }
      131.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          ExifPreferences.fromList(it)
        }
      }
      132.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PigeonSensor.fromList(it)
        }
      }
      133.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PigeonSensorTypeDevice.fromList(it)
        }
      }
      134.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PreviewSize.fromList(it)
        }
      }
      135.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          PreviewSize.fromList(it)
        }
      }
      136.toByte() -> {
        return (readValue(buffer) as? List<Any?>)?.let {
          VideoOptions.fromList(it)
        }
      }
      else -> super.readValueOfType(type, buffer)
    }
  }
  override fun writeValue(stream: ByteArrayOutputStream, value: Any?)   {
    when (value) {
      is AndroidFocusSettings -> {
        stream.write(128)
        writeValue(stream, value.toList())
      }
      is AndroidVideoOptions -> {
        stream.write(129)
        writeValue(stream, value.toList())
      }
      is CupertinoVideoOptions -> {
        stream.write(130)
        writeValue(stream, value.toList())
      }
      is ExifPreferences -> {
        stream.write(131)
        writeValue(stream, value.toList())
      }
      is PigeonSensor -> {
        stream.write(132)
        writeValue(stream, value.toList())
      }
      is PigeonSensorTypeDevice -> {
        stream.write(133)
        writeValue(stream, value.toList())
      }
      is PreviewSize -> {
        stream.write(134)
        writeValue(stream, value.toList())
      }
      is PreviewSize -> {
        stream.write(135)
        writeValue(stream, value.toList())
      }
      is VideoOptions -> {
        stream.write(136)
        writeValue(stream, value.toList())
      }
      else -> super.writeValue(stream, value)
    }
  }
}

/** Generated interface from Pigeon that represents a handler of messages from Flutter. */
interface CameraInterface {
  fun setupCamera(sensors: List<PigeonSensor>, aspectRatio: String, zoom: Double, mirrorFrontCamera: Boolean, enablePhysicalButton: Boolean, flashMode: String, captureMode: String, enableImageStream: Boolean, exifPreferences: ExifPreferences, videoOptions: VideoOptions?, callback: (Result<Boolean>) -> Unit)
  fun checkPermissions(permissions: List<String>): List<String>
  /**
   * Returns given [CamerAwesomePermission] list (as String). Location permission might be
   * refused but the app should still be able to run.
   */
  fun requestPermissions(saveGpsLocation: Boolean, callback: (Result<List<String>>) -> Unit)
  fun getPreviewTextureId(cameraPosition: Long): Long
  fun takePhoto(sensors: List<PigeonSensor>, paths: List<String?>, callback: (Result<Boolean>) -> Unit)
  fun recordVideo(sensors: List<PigeonSensor>, paths: List<String?>, callback: (Result<Unit>) -> Unit)
  fun pauseVideoRecording()
  fun resumeVideoRecording()
  fun receivedImageFromStream()
  fun stopRecordingVideo(callback: (Result<Boolean>) -> Unit)
  fun getFrontSensors(): List<PigeonSensorTypeDevice>
  fun getBackSensors(): List<PigeonSensorTypeDevice>
  fun start(): Boolean
  fun stop(): Boolean
  fun setFlashMode(mode: String)
  fun handleAutoFocus()
  /**
   * Starts auto focus on a point at ([x], [y]).
   *
   * On Android, you can control after how much time you want to switch back
   * to passive focus mode with [androidFocusSettings].
   */
  fun focusOnPoint(previewSize: PreviewSize, x: Double, y: Double, androidFocusSettings: AndroidFocusSettings?)
  fun setZoom(zoom: Double)
  fun setMirrorFrontCamera(mirror: Boolean)
  fun setSensor(sensors: List<PigeonSensor>)
  fun setCorrection(brightness: Double)
  fun getMinZoom(): Double
  fun getMaxZoom(): Double
  fun setCaptureMode(mode: String)
  fun setRecordingAudioMode(enableAudio: Boolean, callback: (Result<Boolean>) -> Unit)
  fun availableSizes(): List<PreviewSize>
  fun refresh()
  fun getEffectivPreviewSize(index: Long): PreviewSize?
  fun setPhotoSize(size: PreviewSize)
  fun setPreviewSize(size: PreviewSize)
  fun setAspectRatio(aspectRatio: String)
  fun setupImageAnalysisStream(format: String, width: Long, maxFramesPerSecond: Double?, autoStart: Boolean)
  fun setExifPreferences(exifPreferences: ExifPreferences, callback: (Result<Boolean>) -> Unit)
  fun startAnalysis()
  fun stopAnalysis()
  fun setFilter(matrix: List<Double>)
  fun isVideoRecordingAndImageAnalysisSupported(sensor: PigeonSensorPosition, callback: (Result<Boolean>) -> Unit)
  fun isMultiCamSupported(): Boolean

  companion object {
    /** The codec used by CameraInterface. */
    val codec: MessageCodec<Any?> by lazy {
      CameraInterfaceCodec
    }
    /** Sets up an instance of `CameraInterface` to handle messages through the `binaryMessenger`. */
    @Suppress("UNCHECKED_CAST")
    fun setUp(binaryMessenger: BinaryMessenger, api: CameraInterface?) {
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setupCamera", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val aspectRatioArg = args[1] as String
            val zoomArg = args[2] as Double
            val mirrorFrontCameraArg = args[3] as Boolean
            val enablePhysicalButtonArg = args[4] as Boolean
            val flashModeArg = args[5] as String
            val captureModeArg = args[6] as String
            val enableImageStreamArg = args[7] as Boolean
            val exifPreferencesArg = args[8] as ExifPreferences
            val videoOptionsArg = args[9] as VideoOptions?
            api.setupCamera(sensorsArg, aspectRatioArg, zoomArg, mirrorFrontCameraArg, enablePhysicalButtonArg, flashModeArg, captureModeArg, enableImageStreamArg, exifPreferencesArg, videoOptionsArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.checkPermissions", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val permissionsArg = args[0] as List<String>
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.checkPermissions(permissionsArg))
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.requestPermissions", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val saveGpsLocationArg = args[0] as Boolean
            api.requestPermissions(saveGpsLocationArg) { result: Result<List<String>> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.getPreviewTextureId", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val cameraPositionArg = args[0].let { if (it is Int) it.toLong() else it as Long }
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.getPreviewTextureId(cameraPositionArg))
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.takePhoto", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val pathsArg = args[1] as List<String?>
            api.takePhoto(sensorsArg, pathsArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.recordVideo", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            val pathsArg = args[1] as List<String?>
            api.recordVideo(sensorsArg, pathsArg) { result: Result<Unit> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                reply.reply(wrapResult(null))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.pauseVideoRecording", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.pauseVideoRecording()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.resumeVideoRecording", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.resumeVideoRecording()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.receivedImageFromStream", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.receivedImageFromStream()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.stopRecordingVideo", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            api.stopRecordingVideo() { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.getFrontSensors", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.getFrontSensors())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.getBackSensors", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.getBackSensors())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.start", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.start())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.stop", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.stop())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setFlashMode", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val modeArg = args[0] as String
            var wrapped: List<Any?>
            try {
              api.setFlashMode(modeArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.handleAutoFocus", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.handleAutoFocus()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.focusOnPoint", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val previewSizeArg = args[0] as PreviewSize
            val xArg = args[1] as Double
            val yArg = args[2] as Double
            val androidFocusSettingsArg = args[3] as AndroidFocusSettings?
            var wrapped: List<Any?>
            try {
              api.focusOnPoint(previewSizeArg, xArg, yArg, androidFocusSettingsArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setZoom", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val zoomArg = args[0] as Double
            var wrapped: List<Any?>
            try {
              api.setZoom(zoomArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setMirrorFrontCamera", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val mirrorArg = args[0] as Boolean
            var wrapped: List<Any?>
            try {
              api.setMirrorFrontCamera(mirrorArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setSensor", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorsArg = args[0] as List<PigeonSensor>
            var wrapped: List<Any?>
            try {
              api.setSensor(sensorsArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setCorrection", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val brightnessArg = args[0] as Double
            var wrapped: List<Any?>
            try {
              api.setCorrection(brightnessArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.getMinZoom", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.getMinZoom())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.getMaxZoom", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.getMaxZoom())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setCaptureMode", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val modeArg = args[0] as String
            var wrapped: List<Any?>
            try {
              api.setCaptureMode(modeArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setRecordingAudioMode", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val enableAudioArg = args[0] as Boolean
            api.setRecordingAudioMode(enableAudioArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.availableSizes", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.availableSizes())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.refresh", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.refresh()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.getEffectivPreviewSize", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val indexArg = args[0].let { if (it is Int) it.toLong() else it as Long }
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.getEffectivPreviewSize(indexArg))
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setPhotoSize", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sizeArg = args[0] as PreviewSize
            var wrapped: List<Any?>
            try {
              api.setPhotoSize(sizeArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setPreviewSize", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sizeArg = args[0] as PreviewSize
            var wrapped: List<Any?>
            try {
              api.setPreviewSize(sizeArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setAspectRatio", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val aspectRatioArg = args[0] as String
            var wrapped: List<Any?>
            try {
              api.setAspectRatio(aspectRatioArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setupImageAnalysisStream", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val formatArg = args[0] as String
            val widthArg = args[1].let { if (it is Int) it.toLong() else it as Long }
            val maxFramesPerSecondArg = args[2] as Double?
            val autoStartArg = args[3] as Boolean
            var wrapped: List<Any?>
            try {
              api.setupImageAnalysisStream(formatArg, widthArg, maxFramesPerSecondArg, autoStartArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setExifPreferences", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val exifPreferencesArg = args[0] as ExifPreferences
            api.setExifPreferences(exifPreferencesArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.startAnalysis", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.startAnalysis()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.stopAnalysis", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              api.stopAnalysis()
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.setFilter", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val matrixArg = args[0] as List<Double>
            var wrapped: List<Any?>
            try {
              api.setFilter(matrixArg)
              wrapped = listOf<Any?>(null)
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.isVideoRecordingAndImageAnalysisSupported", codec)
        if (api != null) {
          channel.setMessageHandler { message, reply ->
            val args = message as List<Any?>
            val sensorArg = PigeonSensorPosition.ofRaw(args[0] as Int)!!
            api.isVideoRecordingAndImageAnalysisSupported(sensorArg) { result: Result<Boolean> ->
              val error = result.exceptionOrNull()
              if (error != null) {
                reply.reply(wrapError(error))
              } else {
                val data = result.getOrNull()
                reply.reply(wrapResult(data))
              }
            }
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
      run {
        val channel = BasicMessageChannel<Any?>(binaryMessenger, "dev.flutter.pigeon.CameraInterface.isMultiCamSupported", codec)
        if (api != null) {
          channel.setMessageHandler { _, reply ->
            var wrapped: List<Any?>
            try {
              wrapped = listOf<Any?>(api.isMultiCamSupported())
            } catch (exception: Throwable) {
              wrapped = wrapError(exception)
            }
            reply.reply(wrapped)
          }
        } else {
          channel.setMessageHandler(null)
        }
      }
    }
  }
}
