import 'dart:async'; // Import for StreamSubscription
import 'dart:io';

// import 'package:better_open_file/better_open_file.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:camerawesome/pigeon.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart'; // Import for getTemporaryDirectory
import 'utils/file_utils.dart';

void main() {
  runApp(const CameraAwesomeApp());
}

class CameraAwesomeApp extends StatelessWidget {
  const CameraAwesomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'camerAwesome',
      home: CameraPage(),
    );
  }
}

class CameraPage extends StatefulWidget {
  const CameraPage({super.key});

  @override
  State<CameraPage> createState() => _CameraPageState();
}

class _CameraPageState extends State<CameraPage> {
  bool _showBlurSlider = false;
  // Add a StreamSubscription to listen to blur changes
  StreamSubscription<double>? _blurSubscription;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _blurSubscription?.cancel(); // Cancel subscription on dispose
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.white,
        child: CameraAwesomeBuilder.awesome(
          onMediaCaptureEvent: (event) {
            switch ((event.status, event.isPicture, event.isVideo)) {
              case (MediaCaptureStatus.capturing, true, false):
                debugPrint('Capturing picture...');
              case (MediaCaptureStatus.success, true, false):
                event.captureRequest.when(
                  single: (single) {
                    debugPrint('Picture saved: ${single.file?.path}');
                  },
                  multiple: (multiple) {
                    multiple.fileBySensor.forEach((key, value) {
                      debugPrint('multiple image taken: $key ${value?.path}');
                    });
                  },
                );
              case (MediaCaptureStatus.failure, true, false):
                debugPrint('Failed to capture picture: ${event.exception}');
              case (MediaCaptureStatus.capturing, false, true):
                debugPrint('Capturing video...');
              case (MediaCaptureStatus.success, false, true):
                event.captureRequest.when(
                  single: (single) {
                    debugPrint('Video saved: ${single.file?.path}');
                  },
                  multiple: (multiple) {
                    multiple.fileBySensor.forEach((key, value) {
                      debugPrint('multiple video taken: $key ${value?.path}');
                    });
                  },
                );
              case (MediaCaptureStatus.failure, false, true):
                debugPrint('Failed to capture video: ${event.exception}');
              default:
                debugPrint('Unknown event: $event');
            }
          },
          saveConfig: SaveConfig.photoAndVideo(
            initialCaptureMode: CaptureMode.photo,
            photoPathBuilder: (sensors) async {
              final Directory extDir = await getTemporaryDirectory();
              final testDir = await Directory(
                '${extDir.path}/camerawesome',
              ).create(recursive: true);
              if (sensors.length == 1) {
                final String filePath =
                    '${testDir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                return SingleCaptureRequest(filePath, sensors.first);
              }
              // Separate pictures taken with front and back camera
              return MultipleCaptureRequest(
                {
                  for (final sensor in sensors)
                    sensor:
                        '${testDir.path}/${sensor.position == SensorPosition.front ? 'front_' : "back_"}${DateTime.now().millisecondsSinceEpoch}.jpg',
                },
              );
            },
            videoOptions: VideoOptions(
              enableAudio: true,
              ios: CupertinoVideoOptions(
                fps: 10,
              ),
              android: AndroidVideoOptions(
                bitrate: 6000000,
                fallbackStrategy: QualityFallbackStrategy.lower,
              ),
            ),
            exifPreferences: ExifPreferences(saveGPSLocation: true),
          ),
          sensorConfig: SensorConfig.single(
            sensor: Sensor.position(SensorPosition.back),
            flashMode: FlashMode.auto,
            aspectRatio: CameraAspectRatios.ratio_4_3,
            zoom: 0.0,
          ),
          enablePhysicalButton: true,
          // filter: AwesomeFilter.AddictiveRed,
          previewAlignment: Alignment.center,
          previewFit: CameraPreviewFit.contain,
          // Add manual exposure control to the default UI
          middleContentBuilder: (state) {
            // _showBlurSlider will now only be controlled by the button and onPreviewTap.
            return Column(
              children: [
                // Top controls row with shutter speed and exposure
                Row(
                  children: [
                    // Shutter speed selector at the top-left
                    Align(
                      alignment: Alignment.topLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16, top: 16),
                        child: AwesomeShutterSpeedSelector(
                          state: state,
                          showResetButton: true,
                          sliderActiveColor: Colors.orange,
                          sliderInactiveColor: Colors.orange.withOpacity(0.3),
                          textColor: Colors.white,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Exposure selector at the top-right
                    Align(
                      alignment: Alignment.topRight,
                      child: Padding(
                        padding: const EdgeInsets.only(right: 16, top: 16),
                        child: AwesomeExposureSelector(
                          state: state,
                          showResetButton: true,
                          sliderActiveColor: Colors.white,
                          sliderInactiveColor: Colors.white.withOpacity(0.3),
                          textColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(), // Pushes content down
                // Blur toggle button
                if (state is PhotoCameraState)
                  Align(
                    alignment: Alignment.bottomRight,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16.0, bottom: 16.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (_showBlurSlider)
                            AwesomeBlurSelector(state: state),
                          RawMaterialButton(
                            onPressed: () {
                              setState(() {
                                _showBlurSlider = !_showBlurSlider;
                              });
                            },
                            elevation: 2.0,
                            fillColor: Colors.white,
                            padding: const EdgeInsets.all(15.0),
                            shape: const CircleBorder(),
                            child: Icon(
                              Icons.blur_on,
                              size: 35.0,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                AwesomeZoomSelector(state: state), // Zoom selector moved above mode selector
                AwesomeCameraModeSelector(state: state), // Mode selector at the bottom
              ],
            );
          },
          onPreviewTapBuilder: (state) => OnPreviewTap(
            onTap: (position, flutterPreviewSize, pixelPreviewSize) {
              if (state is PhotoCameraState) {
                state.focusOnPoint(
                  flutterPosition: position,
                  pixelPreviewSize: pixelPreviewSize,
                  flutterPreviewSize: flutterPreviewSize,
                );
                // When tap-to-focus occurs, blur is reset, so hide the slider
                setState(() {
                  _showBlurSlider = false;
                });
              }
            },
          ),
          onMediaTap: (mediaCapture) {
            mediaCapture.captureRequest.when(
              single: (single) {
                debugPrint('single: ${single.file?.path}');
                single.file?.open();
              },
              multiple: (multiple) {
                multiple.fileBySensor.forEach((key, value) {
                  debugPrint('multiple file taken: $key ${value?.path}');
                  value?.open();
                });
              },
            );
          },
          availableFilters: awesomePresetFiltersList,
        ),
      ),
    );
  }
}
